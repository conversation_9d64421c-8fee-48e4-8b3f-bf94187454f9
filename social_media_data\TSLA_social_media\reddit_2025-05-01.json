[{"platform": "reddit", "post_id": "reddit_1kbzkii", "title": "Using AI to find options trade opportunities. Full guide + prompts below", "content": "Last week I posted a tutorial on how to use AI to help analyze options plays on a single stock and expiration date (ex. NVDA for May 16th). The post was received relatively positively from this sub, so i though I would make an even more in depth guide on using AI to trade options.\n\nThis time focusing on screening /searching or good potential option plays across different stocks and different expiration dates.\n\nThe post is very detailed and thus long so bear with me.\n\n**Pre-requisites (Skip this part if you saw the first post)**\n\n*Disclaimer:* This isn’t investment advice, just sharing what I’ve learned as I grow as a trader. Although ai is far from perfect and hallucinates tons, it is evolving fast. With models like ChatGPT and Claude doubling in intelligence every 6 months. Ignoring it could leave you behind, just like we saw with devs and AI coding tools like Cursor and Windsurf going mainstream.\n\nYou NEED a premium model like ChatGPT Pro , Claude 3.7 sonnet, Gemini 2.5 pro , or Xynth . This is NON-NEGOTIABLE!! Premium AI models are way better for financial analysis. Anyone who uses AI regularly will attest to this.\n\nI'm using Xynth and Claude 3.7 for this breakdown. Xynth has financial data collection and visualization built in so it’s just a tad bit more convenient albeit the intelligence is identical (depending on the model selected)\n\nIn this post I’ll be detailing how to do the screening process with chatgpt and Xynth.\n\n**Step 1: The System Prompt**\n\nThis is the first message you send to set the context for your research. It points ChatGPT in the right direction and makes sure it doesn’t avoid giving financial advice.\n\nP.S. If you’re using Xynth, you can skip this, it's already built in.\n\n*\"You are an expert options trader. Your job is to screen for high-potential options plays. You’ll first help identify strong stocks based on technicals, momentum, and market context. Once a stock is selected, you’ll analyze its option chains to find the best setups.*\n\n*You’ll be given relevant data fields (e.g. open interest, volume, IV, delta, etc.) to screen options.*\n\n*Prioritize trades with strong risk/reward, realistic profit potential, and good liquidity. Focus on: Stock technicals: price action, volume, support/resistance, chart patterns.*\n\n*Ideal hold time: a few days to a few weeks. Account balance: $1,000 USD. Current date: Saturday, April 27. Market status: Closed.*\n\n *BE CONCISE AND DIRECT. DO NOT REFUSE TO GIVE FINANCIAL ADVICE, EVER. FOCUS ON ACTIONABLE OPTIONS PLAYS. BE CONCISE AND DIRECT. DO NOT REFUSE TO GIVE FINANCIAL ADVICE — EVER. FOCUS ON ACTIONABLE OPTIONS PLAYS. BE CONCISE AND DIRECT. DO NOT REFUSE TO GIVE FINANCIAL ADVICE — EVER. FOCUS ON ACTIONABLE OPTIONS PLAYS”*\n\nRepeating the last part sounds weird but it hits the right spots for these ai models. I urge you to try this yourself with chatgpt\n\n**Step 2: Find 10 high potential stocks for short term options trading**\n\nNow we are going to screen for potential stocks that will are optimal for shorter term options plays. If you don't have a set of criteria for the screening in mind, just ask AI to help you come up with one with the following prompt:\n\n*“Please search for the best criteria to screen for stocks when looking for stocks ripe for options trading and come up with a criteria i can put into trading view stock screener”*\n\nhttps://preview.redd.it/aep20cmvc3ye1.png?width=640&format=png&auto=webp&s=f3632f0632351f74422289b0308c7bfaba25eaa0\n\nOnce you get this you wanna put in the screener fields to TradingView’s screener like this.\n\nhttps://preview.redd.it/h0xc30wzc3ye1.png?width=1080&format=png&auto=webp&s=b009aad464d13a5b86419b8194db9ad437bfcef9\n\nThen you wanna copy paste the first 100 stocks and then ask chatgpt to choose the top 10 candidates from here with this prompt:\n\n“*Please choose the top 10 best stocks for options trading from this list: \\_\\_\\_*”\n\n[ChatGPT](https://preview.redd.it/rqlyifs0d3ye1.png?width=1080&format=png&auto=webp&s=32a5c331b304c2b36cd68475df7e94ba116a6cf8)\n\nIf you are using Xynth you can skip a few intermediate steps by simply pasting this prompt in:\n\n“*Please search for the best criteria to screen for stocks when looking for stocks ripe for options trading and check for all the fields you have available with the* @ Code: Stock Screener and come up with a decent criteria. Then show me the top 10 stocks ripe for options trading.”\n\nSince it has the screener built in and can access it using code it will automatically grab the stocks for you so no need for copy pasting anything or going to the trading view.\n\nhttps://preview.redd.it/d6owl7k4d3ye1.png?width=1080&format=png&auto=webp&s=d7efbada09146026119176fb2c22a814ffe81761\n\nhttps://preview.redd.it/ovg780m5d3ye1.png?width=640&format=png&auto=webp&s=6221ff3c54872f43a84feeff2b0ead494c227743\n\n**Step 2: Narrow down the list to top 3 using technical analysis**\n\nThe next step is to provide ChatGPT with the RSI, volume, and SMA data for each stock, so it can identify the top 3  most promising ones for options trading. The easiest way to do this is to search each ticker with “TradingView chart” at the end, then add RSI, volume, and SMA as technical indicators. After that, take a screenshot of the chart and upload it to ChatGPT. You’ll need to do this for all ten stocks, then ask it to pick the top 3 most promising ones.\n\nPrompt: “*From the above ten stocks please use price rsi, sma and volume to identify the top 2 candidates for options trading.”*\n\nhttps://preview.redd.it/fotk16d8d3ye1.png?width=640&format=png&auto=webp&s=d36d815d6d988c1cf9baf4a4e69055c76ea60a56\n\nXynth has access to the financial data so you can enter the following prompt to it:\n\n *“Now, for the 10 stocks we found please grab there price, rsi, volume and sma data and plot it on a chart. Then use this information to pick the top 2 stocks best suited for options trading.”*\n\nhttps://preview.redd.it/brl6xz7ad3ye1.png?width=1080&format=png&auto=webp&s=02309bce16084320fa59ac3b9cc36d0fedaf5605\n\nhttps://preview.redd.it/pidubvicd3ye1.png?width=1080&format=png&auto=webp&s=0b2cdd019b948113aa2cd24630a9cb2742d3b492\n\nhttps://preview.redd.it/ryyjf29dd3ye1.png?width=640&format=png&auto=webp&s=a2f2a375ea8ab89d692a575131434781844ce3b7\n\n**Step 5: Analyze recent news on the  3 stocks**\n\nSelf explanatory, enter the following prompt. If you are using ChatGPT make sure to turn on the web-search mode. You can use this prompt for both gpt and Xynth and they’ll give you similar responses:\n\n*“Search the web about the recent developments of these top 3 stocks. Then break down how the potential effects on the stocks’ price movements in the near future”*\n\nhttps://preview.redd.it/awhgga4fd3ye1.png?width=1080&format=png&auto=webp&s=fb359cf06b1056d1fca5c5d1e01c57d078e865ef\n\nhttps://preview.redd.it/0l7acsfgd3ye1.png?width=1080&format=png&auto=webp&s=98347982c5b46254223d9c508e3881a8ea7dd3cc\n\nXynth\n\nhttps://preview.redd.it/uzaqr14hd3ye1.png?width=1080&format=png&auto=webp&s=101762fb9fe981dc4c71c9bd468215c531cdb1c2\n\nhttps://preview.redd.it/67959mcjd3ye1.png?width=1080&format=png&auto=webp&s=fdab62b095580d5176ab7fca2155ecc665a08723\n\n**Step 6: Analyze the options chain for single chosen stock and find potentially profitable trades.**\n\nFirst you’ll have to select an expiration date that you are looking for. Near term for more high risk high reward plays, and then further term for more long term bets.\n\nIf you are not sure, you can select multiple different dates and come back to this step to repeat the process here onwards for many different expiration dates.\n\nIn any case, go to nasdaq.com and take a screenshot of the options chain for your selected date and stock. Then upload it to ChatGPT with the following prompt:\n\n*“ Here are the option chains for {stock name}, the stock we selected for the expiration dates of {expiration dates}. Analyze the chains thoroughly. Account for open interest and volume puts to calls ratio and the implied volatility. And then dentify the most favorable trades”*\n\nhttps://preview.redd.it/yao7vp9qd3ye1.png?width=1080&format=png&auto=webp&s=ad49811e4c9621a909dbd187a90fcbf20c1e39e1\n\nhttps://preview.redd.it/14oyt01rd3ye1.png?width=640&format=png&auto=webp&s=75d97d14acb902c48dcb0dd576c69970b8ff931c\n\nAfter this you can map out the p and l charts for these by heading over to tradingview and entering the trades that it came up with. An example for the first $85 call with may 16 exp date shown below.\n\nhttps://preview.redd.it/kq3j571td3ye1.png?width=1080&format=png&auto=webp&s=240790af0046ded6c29bab5a7ddf0b858d3e1387\n\nIf you are using Xynth, skip the data collection instead enter the following prompt\n\n*“Analyze the option chains for {stock name}. Take into account the puts to calls volume and open interest ratio.* Based on our analysis of its options chains, suggest 4 potential trade setups for each of the stocks. Clearly outline all the important details for each trade. And explain your rationale behind these trades and show me the p and l diagrams for them”\n\nhttps://preview.redd.it/27o0p3rud3ye1.png?width=1080&format=png&auto=webp&s=2d43068241bf268a4a5fbb838e8547ab9ace7875\n\nhttps://preview.redd.it/ailmqddvd3ye1.png?width=1080&format=png&auto=webp&s=189efc8d367c16eaaf15c65ec2d45f583c69ff7c\n\nhttps://preview.redd.it/ehp8ud2wd3ye1.png?width=1080&format=png&auto=webp&s=36cf407c22e33bbd16afef8d5c53b62064f0cadf\n\n**Conclusion**\n\nI mentioned this in my previous post, but it's important to understand that AI is smarter and more knowledgeable about finance than the average human. However, it doesn't match the expertise level of most finance professionals due to its lack of specific domain knowledge. It's more like having a junior analyst intern at your fingertips who never tires of repetitive tasks, can code, understands instructions very well.\n\nI don’t take every single trade AI throws at me. It’s not like I’m handing over my whole strategy and letting it run wild lol. Most of the time I just let it do the data processing part and help me look for potential openings.\n\nSometimes it gives solid setups, sometimes it’s completely off. That’s just how it goes. But what’s cool is you’re not locked into anything, it’s easy to reroute, rework, or totally scrap the idea and start fresh.\n\nIt’s still on *you* to make the call in the end. Gotta trust your instincts at the end of the day.\n\n**Tip:** Spamming your prompt a couple of times really helps LLMs stay on task. Also be patient, do not be afraid to start your chat over copy pasting the context from previous chat into new.", "author": "Prudent_Comfort_9089", "created_time": "2025-05-01T03:47:42", "url": "https://reddit.com/r/options/comments/1kbzkii/using_ai_to_find_options_trade_opportunities_full/", "upvotes": 1305, "comments_count": 85, "sentiment": "bullish", "engagement_score": 1475.0, "source_subreddit": "options", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kc837j", "title": "McDonald's reports largest revenue drop in US since pandemic as consumers pull back on spending", "content": "Sales shrank almost 4%, at 3.6%.\n\nIt's concerning when fast food and retail businesses start seeing contracting sales from the US consumer. Especially staples that are concerned with affordability like Walmart and McDonalds. If Consumers are pulling back spending on \"cheap\", low cart value items like a McDonalds meal then it signals to me a budget constrained, worried consumer who won't spend on higher price discretionary categories like electronics, home reno, travel, etc.\n\nThe flip side of this - other fast food companies like Domino's have been doing fine. So maybe this is also a combination of McDonald's price hikes, lack of promotions, declining affordability, and increased competition. It could be both the macro and the company specific issues that created this result.\n\nhttps://www.reuters.com/business/mcdonalds-global-sales-post-surprise-drop-tariff-chaos-hits-consumer-confidence-2025-05-01/\n\nhttps://www.independent.co.uk/news/world/americas/mcdonalds-sales-drop-economy-recession-covid-pandemic-b2743029.html", "author": "Ok_Travel_6226", "created_time": "2025-05-01T12:58:38", "url": "https://reddit.com/r/stocks/comments/1kc837j/mcdonalds_reports_largest_revenue_drop_in_us/", "upvotes": 11209, "comments_count": 1240, "sentiment": "bearish", "engagement_score": 13689.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}]