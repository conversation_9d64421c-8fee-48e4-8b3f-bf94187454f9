[{"platform": "reddit", "post_id": "reddit_1kb453a", "title": "Many of you don't remember what The Great Recession was like and it shows", "content": "I say this to the doomers and the delusional optimists  on here.\n\nNo this isn't the bottom\n\nNo, no one knows where the bottom will be. That's why time in the market always beats timing the market.\n\nNo, these things don't happen instantly. The damage this Administration has caused will take months to years to be fully realize. We are still in the very early stages.\n\nNo, we won't have negative growth for the first quarter, it's too soon and we have had recessions where we get positive growth for some quarters, as well. Especially in the beginning.\n\nNo, businesses having a decent first quarter means there's no recession. Not every business will be affected right away, or even majority.\n\nNo, just because you aren't feeling/seeing it in your every day means there will be no Recession. \n\n\nTo prove my point let's look at the 2008 Recession:\n\n - When did the 2008 Recession actually start?\n\nTry December 2007\n\n - When did people start feeling that Recession?\n\nWhile it depends on the person most will tell you Summer/fall 2008\n\n - When did the Bear market Start?\n\n October 09, 2007\n\n - When did is end?\n\nMarch  2009, 17 months later.\n\nEven in the 2008 Recession, we had full quarters with positive growth. 2008 Q2 GDP growth was around 2.3%\n\nAlso the 2008 Recession had multiple dead cat bounces. Some as high as 25% back up. \n\nSo in other words, you all need to calm down.\n\nIt's not going to crash overnight, but it also isn't going to be all roses and sunshine either.\n\nThis isn't a black swan event like COVID, it's being caused by an Administration with severely bad policies that will catch up to us sooner than later. \n\n\n\n\n\n", "author": "alotofironsinthefire", "created_time": "2025-04-30T01:02:48", "url": "https://reddit.com/r/stocks/comments/1kb453a/many_of_you_dont_remember_what_the_great/", "upvotes": 26135, "comments_count": 1779, "sentiment": "bullish", "engagement_score": 29693.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kbesxa", "title": "I talked with a Chinese factory owner to see what’s happening on the ground. TL;DR: China has mobilized tons of resources to exporters but there’s some signs of pain", "content": "I’m an American in China but have been seeing very little foreign reporting about what’s happening in China, which is weird to me because it seems pretty important to get a clear picture of the trade war. So here’s what I learned from him.\n\n**Gov support**\n\n-The local government is helping and giving him low interest rate loans / short term financial support. This type of thing is very common throughout China right now - lots of local govs are mobilizing to support exporters thru the tariff war. <PERSON>, central gov minister, says central government help is probably coming too \n\n-Guangzhou, and I assume others too, has also organized tons of seminars since Liberation Day with buyers from Europe, Latin America, and Asia. A big source of replacement demand for his factory has been from these\n\n-He’s getting lots of buyers from TikTok shops overseas. It seems like TikTok is helping a lot to mitigate the demand impact \n\n-The news story of Walmart, Target, Costco, etc. resuming some orders is true, he confirmed it. But the amounts are **very, very** small\n\n**Pain**\n\n-He’s already re-evaluating employees and firing some soon. He said most exporters in Guangzhou are cutting some employees, but extremely US dependent exporters have furloughed most of their workforces until mid May\n\n**Some good news for the U.S?**\n\n-Before Liberation Day he was building a factory in the US and has now fully committed to it. But it’s having issues since the raw materials are sourced from China and they’re getting hit with tariffs\n\n[The full interview is here](https://www.yaphete.com/p/live-from-the-trade-war-2-chinas), I didn’t write everything here. Knowing the relative strength and ability of China or the US to withstand the tariff war is super important to trading right now I believe", "author": "TheOneTrueThrowaway1", "created_time": "2025-04-30T12:06:53", "url": "https://reddit.com/r/investing/comments/1kbesxa/i_talked_with_a_chinese_factory_owner_to_see/", "upvotes": 3560, "comments_count": 425, "sentiment": "bullish", "engagement_score": 4410.0, "source_subreddit": "investing", "hashtags": null, "ticker": "TSLA"}, {"platform": "reddit", "post_id": "reddit_1kbqdtr", "title": "<PERSON> says he’ll blame <PERSON><PERSON> again for 2nd quarter GDP after blaming him for Q1 drop", "content": "https://www.cnbc.com/2025/04/30/trump-gdp-tariffs-biden-overhang.html\n\n>President <PERSON> on Wednesday blamed former President <PERSON> for the U.S. economy contracting in the first quarter of 2025 — and suggested he will blame <PERSON><PERSON> again for the second quarter’s results.\n>\n>“This is <PERSON><PERSON>,” <PERSON> said after the Commerce Department reported gross domestic product declined in the first three months of this year.\n>\n>“And you could even say the next quarter is sort of Biden because it doesn’t just happen on a daily or an hourly basis,” he said during a Cabinet meeting at the White House.\n>\n><PERSON> noted that he did not take office until late January.\n>\n>“The stock market in this case is, it says how bad the situation we inherited,” he said. “This is a quarter that we looked at today, and I, we took, all of us, together, we came in on January 20th.”\n\nDoes this mean Q2 will be negative GDP growth too? If he's preemptively setting up blame for a quarter we're only one month into\n\nI assume they have access to economic data that we don't", "author": "Fidler_2K", "created_time": "2025-04-30T20:22:51", "url": "https://reddit.com/r/stocks/comments/1kbqdtr/trump_says_hell_blame_biden_again_for_2nd_quarter/", "upvotes": 36633, "comments_count": 1337, "sentiment": "bearish", "engagement_score": 39307.0, "source_subreddit": "stocks", "hashtags": null, "ticker": "TSLA"}]